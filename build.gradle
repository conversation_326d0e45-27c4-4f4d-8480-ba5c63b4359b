// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    ext.kotlin_version = '1.6.21'
    repositories {
        mavenCentral()
        google()
        jcenter()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:8.10.1'
        classpath 'com.google.gms:google-services:4.3.15'
        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath "org.jetbrains.kotlin:kotlin-android-extensions:$kotlin_version"
        classpath 'com.google.firebase:perf-plugin:1.4.1'
    }
}

allprojects {
    repositories {
        google()
        jcenter()
        maven { url "https://maven.google.com" }
        maven { url "https://maven.fabric.io/public" }
        maven { url "https://dl.bintray.com/hani-momanii/maven" }
        maven { url "https://jitpack.io" }
        mavenCentral()
    }
}

subprojects {
    afterEvaluate { project ->
        project.tasks.whenTaskAdded { task ->
            if (task.name == 'mergeProductionReleaseResources') {
                task.dependsOn('processProductionReleaseGoogleServices')
            }
        }
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}
