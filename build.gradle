// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    ext.kotlin_version = '1.8.10'
    repositories {
        google()
        mavenCentral()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:8.1.4'
        classpath 'com.google.gms:google-services:4.4.0'
        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath 'com.google.firebase:perf-plugin:1.4.2'
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
        maven { url "https://jitpack.io" }
    }
}

subprojects {
    afterEvaluate { project ->
        project.tasks.whenTaskAdded { task ->
            if (task.name == 'mergeProductionReleaseResources') {
                task.dependsOn('processProductionReleaseGoogleServices')
            }
        }
    }
}

task clean(type: Delete) {
    delete rootProject.buildDir
}
