apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-android-extensions'

android {
    signingConfigs {
        playstore {
            keyAlias 'schnellXsmanagerKey'
            keyPassword 'schnell-xsmanager'
            storeFile file('docs/schnell-xsmanager-keystore.jks')
            storePassword 'schnell-xsmanager'
        }
        dev {
            keyAlias 'com.schnell.xsmanager'
            keyPassword 'X@erp^&10))@P123|R!'
            storeFile file('docs/schnell-xsmanager-dev-keystore.jks')
            storePassword 'Xs&14$Dev*8@0)!E%#p|'
        }
    }
    namespace = "com.schnell.xsmanager"
    compileSdkVersion 34
    buildToolsVersion '34.0.0'
    buildFeatures {
        buildConfig = true
        viewBinding = true
    }
    defaultConfig {
        applicationId "com.schnell.xsmanager"
        minSdkVersion 21
        targetSdkVersion 34
        versionCode 65
        versionName '2.16.5'
        testInstrumentationRunner 'androidx.test.runner.AndroidJUnitRunner'
        signingConfig signingConfigs.dev
        versionNameSuffix ''
    }
    buildTypes {
        debug {
            debuggable true
            minifyEnabled false
            shrinkResources false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
        release {
            signingConfig signingConfigs.playstore
            debuggable false
            minifyEnabled false
            shrinkResources false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'

        }
    }
    flavorDimensions "default"
    productFlavors {
        dev {
            getProps('./config/dev.props').each { p ->
                buildConfigField 'String', p.key, p.value
            }
            versionNameSuffix ' dev'
            signingConfig signingConfigs.dev
        }
        qa {
            getProps('./config/qa.props').each { p ->
                buildConfigField 'String', p.key, p.value
            }
            versionNameSuffix ' qa'
            signingConfig signingConfigs.dev
        }
        uat {
            getProps('./config/uat.props').each { p ->
                buildConfigField 'String', p.key, p.value
            }
            versionNameSuffix ' uat'
            signingConfig signingConfigs.dev
        }
        production {
            getProps('./config/prod.props').each { p ->
                buildConfigField 'String', p.key, p.value
            }
            versionNameSuffix ' beta'
            signingConfig signingConfigs.playstore
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }
}

def getProps(path) {
    Properties props = new Properties()
    props.load(new FileInputStream(file(path)))
    return props
}

dependencies {
    implementation 'com.google.firebase:firebase-storage-ktx:20.3.0'
    implementation 'androidx.core:core-ktx:1.12.0'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.5.1'
    testImplementation 'junit:junit:4.13.2'
    implementation 'com.github.PhilJay:MPAndroidChart:v3.1.0'
    implementation 'com.google.android.material:material:1.11.0'
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'androidx.cardview:cardview:1.0.0'
    implementation 'androidx.recyclerview:recyclerview:1.3.2'
    implementation 'androidx.legacy:legacy-support-v4:1.0.0'
    implementation 'androidx.gridlayout:gridlayout:1.0.0'
    implementation 'com.android.volley:volley:1.2.1'
    implementation 'com.google.code.gson:gson:2.10.1'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation 'com.google.firebase:firebase-messaging:23.4.0'
    implementation 'com.google.firebase:firebase-core:21.1.1'
    implementation "org.jetbrains.kotlin:kotlin-stdlib:$kotlin_version"
    implementation "me.leolin:ShortcutBadger:1.1.22@aar"
    implementation 'com.google.firebase:firebase-analytics:21.5.0'
    implementation 'com.google.firebase:firebase-perf:20.5.1'
    implementation 'com.google.firebase:firebase-auth:22.3.0'
    implementation 'androidx.browser:browser:1.7.0'
    implementation 'com.google.firebase:firebase-storage:20.3.0'
    implementation 'me.relex:circleindicator:2.1.6'
    androidTestImplementation 'androidx.test.ext:junit:1.1.5'
    androidTestImplementation 'androidx.compose.ui:ui-test-junit4:1.5.8'

}
apply plugin: 'com.google.gms.google-services'
apply plugin: 'com.google.firebase.firebase-perf'