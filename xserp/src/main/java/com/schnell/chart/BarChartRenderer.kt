package com.schnell.chart

import android.graphics.Canvas
import android.graphics.Color
import android.graphics.RectF
import android.util.Log
import com.github.mikephil.charting.animation.ChartAnimator
import com.github.mikephil.charting.interfaces.dataprovider.BarDataProvider
import com.github.mikephil.charting.interfaces.datasets.IBarDataSet
import com.github.mikephil.charting.utils.Utils
import com.github.mikephil.charting.utils.ViewPortHandler

/**
 * @since 21/3/17.
 * Specially customized to draw Rounded Bar refer this.setRoundedCorner(rx, ry)
 */

class BarChartRenderer(chart: BarDataProvider, animator: ChartAnimator, viewPortHandler: ViewPortHandler) : com.github.mikephil.charting.renderer.BarChartRenderer(chart, animator, viewPortHandler) {

    private var rx = 8f
    private var ry = 8f
    private var roundedBar = false

    private var drawTotalStackValue = false
    private var stackValueColor = Color.BLACK
    private var valueDivider = 1f

    private val mBarShadowRectBuffer = RectF()

    /**
     * Sample Display values in hundreds or lakhs
     * value/v {v > 0 && v % 10 == 0}
     *
     * @param v should be multiples of 10
     */
    fun setValuesType(v: Float) {
        valueDivider = if (v > 0 && v % 10 == 0f) {
            v
        } else {
            1f
        }
    }

    /**
     * @param draw true to draw total value above the bar stack
     */
    fun setDrawTotalStackValue(draw: Boolean) {
        this.drawTotalStackValue = draw
    }

    /**
     * Draw total value above the bar stack with this color
     *
     * @param color default will be Color.Black
     */
    fun setStackValueColor(color: Int) {
        this.stackValueColor = color
    }

    /**
     * Call this method with values in floating numbers<BR></BR>
     *
     * @param rx min 3f
     * @param ry min 3f
     */
    fun setRoundedCorner(rx: Float, ry: Float) {
        this.rx = rx
        this.ry = ry
        roundedBar = rx > 0 || ry > 0
    }

    override fun drawDataSet(c: Canvas, dataSet: IBarDataSet, index: Int) {

        val trans = mChart.getTransformer(dataSet.axisDependency)

        mBarBorderPaint.color = dataSet.barBorderColor
        mBarBorderPaint.strokeWidth = Utils.convertDpToPixel(dataSet.barBorderWidth)

        val drawBorder = dataSet.barBorderWidth > 0f

        val phaseX = mAnimator.phaseX
        val phaseY = mAnimator.phaseY

        // draw the bar shadow before the values
        if (mChart.isDrawBarShadowEnabled) {
            mShadowPaint.color = dataSet.barShadowColor

            val barData = mChart.barData

            val barWidth = barData.barWidth
            val barWidthHalf = barWidth / 2.0f
            var x: Float

            var i = 0
            val count = Math.min(Math.ceil((dataSet.entryCount.toFloat() * phaseX).toDouble()).toInt(), dataSet.entryCount)
            while (i < count) {

                val e = dataSet.getEntryForIndex(i)

                x = e.x

                mBarShadowRectBuffer.left = x - barWidthHalf
                mBarShadowRectBuffer.right = x + barWidthHalf

                trans.rectValueToPixel(mBarShadowRectBuffer)

                if (!mViewPortHandler.isInBoundsLeft(mBarShadowRectBuffer.right)) {
                    i++
                    continue
                }

                if (!mViewPortHandler.isInBoundsRight(mBarShadowRectBuffer.left))
                    break

                mBarShadowRectBuffer.top = mViewPortHandler.contentTop()
                mBarShadowRectBuffer.bottom = mViewPortHandler.contentBottom()

                c.drawRect(mBarShadowRectBuffer, mShadowPaint)
                i++
            }
        }

        // initialize the buffer
        val buffer = mBarBuffers[index]
        buffer.setPhases(phaseX, phaseY)
        buffer.setDataSet(index)
        buffer.setInverted(mChart.isInverted(dataSet.axisDependency))
        buffer.setBarWidth(mChart.barData.barWidth)

        buffer.feed(dataSet)

        trans.pointValuesToPixel(buffer.buffer)

        val isSingleColor = dataSet.colors.size == 1

        if (isSingleColor) {
            mRenderPaint.color = dataSet.color
        }

        var j = 0
        while (j < buffer.size()) {

            if (!mViewPortHandler.isInBoundsLeft(buffer.buffer[j + 2])) {
                j += 4
                continue
            }

            if (!mViewPortHandler.isInBoundsRight(buffer.buffer[j]))
                break

            if (!isSingleColor) {
                // Set the color for the currently drawn value. If the index
                // is out of bounds, reuse colors.
                mRenderPaint.color = dataSet.getColor(j / 4)
            }

            //            c.drawRect(buffer.buffer[j], buffer.buffer[j + 1], buffer.buffer[j + 2],
            //                    buffer.buffer[j + 3], mRenderPaint);
            val r = RectF(buffer.buffer[j], buffer.buffer[j + 1], buffer.buffer[j + 2],
                    buffer.buffer[j + 3])

            val currentBar = (j / 4).toFloat()
            if (roundedBar) {
                //                Log.e("Current bar", " " + r );
                c.drawRoundRect(r, rx, ry, mRenderPaint)
                if (dataSet.stackSize > 1) {
                    when {
                        currentBar % dataSet.stackSize == 0f -> // draw skipped top corners
                            // Log.e("Current bar", " Bottom "  + currentBar  );
                            c.drawRect(buffer.buffer[j], buffer.buffer[j + 1], buffer.buffer[j + 2],
                                    buffer.buffer[j + 3] - ry * 2, mRenderPaint)
                        currentBar % dataSet.stackSize == (dataSet.stackSize - 1).toFloat() -> // draw skipped bottom corners
                            // Log.e("Current bar", " Top "  + currentBar  );
                            // ry is for round rect corner size that may loss 1 or 2 pixels to avoid make it double here
                            c.drawRect(buffer.buffer[j], buffer.buffer[j + 1] + ry * 2, buffer.buffer[j + 2],
                                    buffer.buffer[j + 3], mRenderPaint)
                        else -> // Log.e("Current bar", " Mid "  + currentBar  );
                            c.drawRect(r, mRenderPaint)
                    }
                }
            } else {
                c.drawRect(r, mRenderPaint)
            }

            if (drawBorder) {
                if (roundedBar) {
                    c.drawRoundRect(r, rx, ry, mBarBorderPaint)
                } else {
                    c.drawRect(r, mBarBorderPaint)
                }
            }
            j += 4
        }
    }

    override fun drawValues(c: Canvas) {

        // if values are drawn
        if (isDrawingValuesAllowed(mChart)) {

            val dataSets = mChart.barData.dataSets

            val valueOffsetPlus = Utils.convertDpToPixel(4.5f)
            var posOffset: Float
            var negOffset: Float
            val drawValueAboveBar = mChart.isDrawValueAboveBarEnabled

            for (i in 0 until mChart.barData.dataSetCount) {

                val dataSet = dataSets[i]

                if (!shouldDrawValues(dataSet))
                    continue

                // apply the text-styling defined by the DataSet
                applyValueTextStyle(dataSet)

                val isInverted = mChart.isInverted(dataSet.axisDependency)

                // calculate the correct offset depending on the draw position of
                // the value
                val valueTextHeight = Utils.calcTextHeight(mValuePaint, "8").toFloat()
                posOffset = if (drawValueAboveBar) -valueOffsetPlus else valueTextHeight + valueOffsetPlus
                negOffset = if (drawValueAboveBar) valueTextHeight + valueOffsetPlus else -valueOffsetPlus

                if (isInverted) {
                    posOffset = -posOffset - valueTextHeight
                    negOffset = -negOffset - valueTextHeight
                }

                // get the buffer
                val buffer = mBarBuffers[i]

                val phaseY = mAnimator.phaseY

                // if only single values are drawn (sum)
                if (!dataSet.isStacked) {

                    var j = 0
                    while (j < buffer.buffer.size * mAnimator.phaseX) {

                        val x = (buffer.buffer[j] + buffer.buffer[j + 2]) / 2f

                        if (!mViewPortHandler.isInBoundsRight(x))
                            break

                        if (!mViewPortHandler.isInBoundsY(buffer.buffer[j + 1]) || !mViewPortHandler.isInBoundsLeft(x)) {
                            j += 4
                            continue
                        }

                        val entry = dataSet.getEntryForIndex(j / 4)
                        val `val` = entry.y
                        val ymin = if (`val` >= 0) buffer.buffer[j + 1] + posOffset else buffer.buffer[j + 3] + negOffset
                        drawValue(c, dataSet.valueFormatter.getFormattedValue(`val` / valueDivider, entry, i, mViewPortHandler), x, ymin,
                                dataSet.getValueTextColor(j / 4))
                        try {
                            if (drawTotalStackValue) {
                                mValuePaint.color = stackValueColor
                                if (`val` > 0) {
                                    c.drawText(entry.data.toString(), x, ymin - 50, mValuePaint)
                                } else {
                                    c.drawText(entry.data.toString(), x, ymin + 50, mValuePaint)
                                }
                            }

                        } catch (e: Exception) {
                            // No need to handle
                            Log.e("Bar chart", e.message!!)
                        }
                        j += 4
                    }

                    // if we have stacks
                } else {

                    val trans = mChart.getTransformer(dataSet.axisDependency)

                    var bufferIndex = 0
                    var index = 0

                    while (index < dataSet.entryCount * mAnimator.phaseX) {

                        val entry = dataSet.getEntryForIndex(index)

                        val vals = entry.yVals
                        val x = (buffer.buffer[bufferIndex] + buffer.buffer[bufferIndex + 2]) / 2f

                        val color = dataSet.getValueTextColor(index)

                        // we still draw stacked bars, but there is one
                        // non-stacked
                        // in between
                        if (vals == null) {

                            if (!mViewPortHandler.isInBoundsRight(x))
                                break

                            if (!mViewPortHandler.isInBoundsY(buffer.buffer[bufferIndex + 1]) || !mViewPortHandler.isInBoundsLeft(x))
                                continue

                            drawValue(c, dataSet.valueFormatter.getFormattedValue(entry.y / valueDivider, entry, i, mViewPortHandler), x,
                                    buffer.buffer[bufferIndex + 1] + if (entry.y >= 0) posOffset else negOffset,
                                    color)
                            // draw stack values
                        } else {

                            val transformed = FloatArray(vals.size * 2)

                            var posY = 0f
                            var negY = -entry.negativeSum

                            run {
                                var k = 0
                                var idx = 0
                                while (k < transformed.size) {

                                    val value = vals[idx]
                                    val y: Float

                                    if (value == 0.0f && (posY == 0.0f || negY == 0.0f)) {
                                        // Take care of the situation of a 0.0 value, which overlaps a non-zero bar
                                        y = value
                                    } else if (value >= 0.0f) {
                                        posY += value
                                        y = posY
                                    } else {
                                        y = negY
                                        negY -= value
                                    }

                                    transformed[k + 1] = y * phaseY
                                    k += 2
                                    idx++
                                }
                            }

                            trans.pointValuesToPixel(transformed)
                            var ymin = 0f
                            var k = 0
                            while (k < transformed.size) {

                                val `val` = vals[k / 2]
                                val drawBelow = `val` == 0.0f && negY == 0.0f && posY > 0.0f || `val` < 0.0f
                                val y = transformed[k + 1] + if (drawBelow) negOffset else posOffset

                                if (!mViewPortHandler.isInBoundsRight(x))
                                    break

                                if (!mViewPortHandler.isInBoundsY(y) || !mViewPortHandler.isInBoundsLeft(x)) {
                                    k += 2
                                    continue
                                }
                                // If value is 0 no need to display in stacked grid
                                if (dataSet.stackSize == 1 || vals[k / 2] > 0)
                                    if (k == 0) {
                                        drawValue(c, dataSet.valueFormatter.getFormattedValue(vals[k / 2], entry, i, mViewPortHandler), x, y, color)
                                    } else {
                                        drawValue(c, dataSet.valueFormatter.getFormattedValue(vals[k / 2], entry, i, mViewPortHandler), x, y + 37, color)
                                    }
                                try {
                                    if (drawTotalStackValue && k / 2 % dataSet.stackSize == dataSet.stackSize - 1) {
                                        mValuePaint.color = stackValueColor
                                        if (vals[k / 2] == 0f) {
                                            c.drawText(entry.data.toString(), x, if (y < ymin) y else ymin - 50, mValuePaint)
                                        } else {
                                            c.drawText(entry.data.toString(), x, y - 50, mValuePaint)
                                        }
                                    }

                                    ymin = y
                                } catch (e: Exception) {
                                    // No need to handle
                                    Log.e("Bar chart", e.message!!)
                                }

                                k += 2
                            }
                        }

                        bufferIndex = if (vals == null) bufferIndex + 4 else bufferIndex + 4 * vals.size
                        index++
                    }
                }
            }
        }
    }

    companion object {
        const val HUNDREDS = 100f
        const val THOUSANDS = 1000f
        const val LAKHS = 100000f
    }
}
