package com.schnell.widget

import android.annotation.SuppressLint
import android.app.Dialog
import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.View
import android.view.Window
import android.view.WindowManager
import android.widget.Button
import android.widget.TextView
import android.widget.ImageView
import com.schnell.http.Response
import com.schnell.xsmanager.R


/**
 * @since 31/10/2020.
 * SignUpWarningDialog dialog
 */

class SignUpDialog(context: Context, var response: Response? = null, var inWhich: Int = 0, var callback: CallBack? = null) : Dialog(
        context), View.OnClickListener {

    private lateinit var okBtn: Button
    private lateinit var requestTitle: TextView
    private lateinit var requestMessage: TextView
    private lateinit var payment_icon: ImageView

    @SuppressLint("SetTextI18n")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        try {
            requestWindowFeature(Window.FEATURE_NO_TITLE)
            window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
            setContentView(R.layout.request_extension_dialog)
            window?.setLayout(WindowManager.LayoutParams.MATCH_PARENT,
                    WindowManager.LayoutParams.WRAP_CONTENT)

            // Initialize views
            okBtn = findViewById(R.id.okBtn)
            requestTitle = findViewById(R.id.requestTitle)
            requestMessage = findViewById(R.id.requestMessage)
            payment_icon = findViewById(R.id.payment_icon)

            okBtn.setOnClickListener(this)
            if (inWhich == REGISTRATION_COMPLETION_DIALOG) {
                requestTitle.text = response?.message
                requestMessage.visibility = View.VISIBLE
                requestMessage.text = response?.customMessage
            } else if (inWhich == REGISTRATION_SUCCESS_DIALOG) {
                requestTitle.text = response?.message
                requestMessage.visibility = View.VISIBLE
                requestMessage.text = response?.customMessage
            } else if (inWhich == EMAIL_FAILD_DIALOG) {
                requestTitle.visibility = View.GONE
                requestMessage.visibility = View.VISIBLE
                requestMessage.text = response?.customMessage
                payment_icon.setImageResource(R.mipmap.warning)
            } else {
                requestTitle.visibility = View.GONE
                requestMessage.visibility = View.VISIBLE
                requestMessage.text = context.getString(R.string.email_address_message)
                payment_icon.setImageResource(R.mipmap.warning)
            }

            okBtn.setOnClickListener {
                dismiss()
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    override fun onClick(p0: View?) {
        if (p0?.id == R.id.cancel || p0?.id == R.id.okBtn) {
            dismiss()

        } else if (p0?.id == R.id.yesBtn) {
            callback?.onClick()
            dismiss()
        }
    }

    interface CallBack {
        fun onClick()
    }

    companion object {
        val EMAIL_DIALOG = 0
        val REGISTRATION_SUCCESS_DIALOG = 1
        val REGISTRATION_COMPLETION_DIALOG = 2
        val EMAIL_FAILD_DIALOG = 3
    }


}