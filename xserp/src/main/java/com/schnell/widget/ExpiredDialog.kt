package com.schnell.widget

import android.app.Activity
import android.app.Dialog
import android.content.DialogInterface
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.os.CountDownTimer
import android.view.View
import android.view.ViewGroup
import android.view.Window
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import android.widget.Button
import androidx.cardview.widget.CardView
import androidx.core.content.ContextCompat
import com.schnell.util.DateUtil
import com.schnell.util.Utility
import com.schnell.xsmanager.AppPreference
import com.schnell.xsmanager.R
import com.schnell.xsmanager.model.User
import java.util.*


/**
 * @since 8/6/17.
 * Expired dialog
 */

class ExpiredDialog(var context: Activity, private var callback: Callback) : Dialog(
        context), View.OnClickListener, DialogInterface.OnDismissListener {
    private var timer: CountDownTimer? = null
    private var rootLayout: LinearLayout? = null
    private var user: User? = null
    private var reason: String? = null

    // View declarations
    private lateinit var subscribePlan: TextView
    private lateinit var dialogTitle: TextView
    private lateinit var subscribeBtn: Button
    private lateinit var requestBtn: Button
    private lateinit var daysCount: TextView
    private lateinit var days: TextView
    private lateinit var cardView: CardView
    private lateinit var seconds: TextView
    private lateinit var minus: TextView
    private lateinit var hours: TextView
    private lateinit var hoursCount: TextView
    private lateinit var secondsCount: TextView
    private lateinit var minusColon: TextView
    private lateinit var minusCount: TextView
    private lateinit var hoursColon: TextView
    private lateinit var before: TextView
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        try {
            requestWindowFeature(Window.FEATURE_NO_TITLE)
            window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
            setContentView(R.layout.expired_dialog)
            val layout: LinearLayout = findViewById(R.id.cornerLayout)
            val params: ViewGroup.LayoutParams? = layout.layoutParams
            params?.width = Utility.getDeviceWidth(activity = context) - 50
            layout.layoutParams = params
            setCancelable(false)
            setCanceledOnTouchOutside(false)
            user = AppPreference.getLoginUser(context)
            rootLayout = findViewById(R.id.layout)

            // Initialize views
            subscribePlan = findViewById(R.id.subscribePlan)
            dialogTitle = findViewById(R.id.dialogTitle)
            subscribeBtn = findViewById(R.id.subscribeBtn)
            requestBtn = findViewById(R.id.requestBtn)
            daysCount = findViewById(R.id.daysCount)
            days = findViewById(R.id.days)
            cardView = findViewById(R.id.cardView)
            seconds = findViewById(R.id.seconds)
            minus = findViewById(R.id.minus)
            hours = findViewById(R.id.hours)
            hoursCount = findViewById(R.id.hoursCount)
            secondsCount = findViewById(R.id.secondsCount)
            minusColon = findViewById(R.id.minusColon)
            minusCount = findViewById(R.id.minusCount)
            hoursColon = findViewById(R.id.hoursColon)
            before = findViewById(R.id.before)
            if (user?.isExpired!!) {
                if (user?.plan == context.getString(R.string.trial)) {
                    subscribePlan.text = context.getString(R.string.trail_period_expired)
                } else {
                    subscribePlan.text = context.getString(R.string.subscription_expired_since)
                    dialogTitle.text = context.getString(R.string.subscribe_renewal)
                }
            } else {
                if (user?.plan == context.getString(R.string.trial)) {
                    subscribePlan.text = context.getString(R.string.trial_period_expires)
                } else {
                    subscribePlan.text = context.getString(R.string.subscription_expires)
                    dialogTitle.text = context.getString(R.string.subscribe_renewal)
                }
            }
            if (user?.plan == context.getString(R.string.trial)) {
                subscribeBtn.text = context.getString(R.string.subscribe)
                requestBtn.text = context.getString(R.string.trial_extension)
                reason = "trial_extension"
            } else {
                subscribeBtn.text = context.getString(R.string.renew)
                requestBtn.text = context.getString(R.string.grace_period)
                reason = "grace_period"
            }
            setTimer(DateUtil.parseApiDateTime(user?.expiredOn!!)!!)
            (findViewById<ImageView>(R.id.closeImg)).setOnClickListener {
                timer!!.cancel()
                dismiss()
            }
            subscribeBtn.setOnClickListener(this)
            requestBtn.setOnClickListener(this)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun setTimer(expiredOn: Date) {
        if (timer != null) {
            timer?.cancel()
        }
        timer = object : CountDownTimer(48000 * 3600, 1000) {
            override fun onTick(millisUntilFinished: Long) {
                try {
                    val diff = Utility.getTimeDifference(expiredOn, Date())
                    if (diff[0] == 0) {
                        daysCount.text = ""
                        days.visibility = View.GONE
                    } else {
                        daysCount.text = String.format("%02d", diff[0])
                        days.visibility = View.VISIBLE
                    }
                    user?.isExpired = diff[4] == 1
                    showTimer(diff)
                    if (user?.isExpired!!) {
                        viewColors()
                    }
                } catch (e: Exception) {

                }
            }

            override fun onFinish() {

            }
        }
        timer?.start()
    }

    private fun showTimer(timer: Array<Int>) {
        hoursCount.text = String.format("%02d", timer[1])
        minusCount.text = String.format("%02d", timer[2])
        secondsCount.text = String.format("%02d", timer[3])
    }

    private fun viewColors() {
        cardView.setCardBackgroundColor(
                ContextCompat.getColor(context, R.color.redSubscribeBgColor))
        subscribePlan.text = context.getString(R.string.subscription_expired_since)
        subscribePlan.setTextColor(Color.WHITE)
        days.setTextColor(Color.WHITE)
        daysCount.setTextColor(Color.WHITE)
        seconds.setTextColor(Color.WHITE)
        minus.setTextColor(Color.WHITE)
        hours.setTextColor(Color.WHITE)
        hoursCount.setTextColor(Color.WHITE)
        dialogTitle.setTextColor(Color.WHITE)
        secondsCount.setTextColor(Color.WHITE)
        minusColon.setTextColor(Color.WHITE)
        minusCount.setTextColor(Color.WHITE)
        hoursColon.setTextColor(Color.WHITE)
        before.visibility = View.VISIBLE
    }

    interface Callback {
        fun onClickBtn(reason: String)
    }

    override fun onClick(p0: View?) {
        if (p0?.id == R.id.subscribeBtn) {
            callback.onClickBtn("")
        } else if (p0?.id == R.id.requestBtn) {
            callback.onClickBtn(reason!!)
        }
        dismiss()
        timer!!.cancel()
    }

    override fun onDismiss(p0: DialogInterface?) {
    }


}