package com.schnell.widget

import android.app.Dialog
import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.Window
import android.view.WindowManager
import android.widget.Toast
import android.widget.Button
import android.widget.EditText
import com.schnell.xsmanager.R


/**
 * @since 28/09/2020.
 * PaymentDialog dialog
 */

class PaymentDialog(context: Context, var amount: String, private var callback: Callback) : Dialog(
        context) {

    private lateinit var payBtn: Button
    private lateinit var cancelBtn: Button
    private lateinit var mobileNo: EditText

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        try {
            requestWindowFeature(Window.FEATURE_NO_TITLE)
            window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
            setContentView(R.layout.payment_dialog)
            window?.setLayout(WindowManager.LayoutParams.MATCH_PARENT,
                    WindowManager.LayoutParams.WRAP_CONTENT)

            // Initialize views
            payBtn = findViewById(R.id.payBtn)
            cancelBtn = findViewById(R.id.cancelBtn)
            mobileNo = findViewById(R.id.mobileNo)

            payBtn.setOnClickListener {
                if (mobileNo.text!!.isEmpty()) {
                    Toast.makeText(context, context.getString(R.string.please_enter_mobile),
                            Toast.LENGTH_SHORT).show()
                    return@setOnClickListener
                }
                callback.onClickBtn(mobileNo = mobileNo.text.toString())
                dismiss()
            }
            cancelBtn.setOnClickListener {
                dismiss()
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }


    interface Callback {
        fun onClickBtn(mobileNo: String)
    }


}