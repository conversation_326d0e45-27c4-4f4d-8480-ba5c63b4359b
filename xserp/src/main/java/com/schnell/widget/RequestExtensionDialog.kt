package com.schnell.widget

import android.annotation.SuppressLint
import android.app.Dialog
import android.content.Context
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.os.Bundle
import android.view.View
import android.view.Window
import android.view.WindowManager
import android.widget.Button
import android.widget.TextView
import android.widget.ImageView
import android.widget.LinearLayout
import com.schnell.util.DateUtil
import com.schnell.xsmanager.AppPreference
import com.schnell.xsmanager.R
import com.schnell.xsmanager.model.User


/**
 * @since 28/09/2020.
 * PaymentDialog dialog
 */

class RequestExtensionDialog(context: Context, var response: String, var inWhich: Int = 1, var isReachSales: Boolean? = false, var callback: CallBack? = null) : Dialog(
        context), View.OnClickListener {

    private lateinit var okBtn: Button
    private lateinit var requestTitle: TextView
    private lateinit var requestDescription: TextView
    private lateinit var requestMessage: TextView
    private lateinit var payment_icon: ImageView
    private lateinit var confirm_buttonLayout: LinearLayout
    private lateinit var yesBtn: Button
    private lateinit var cancel: Button

    @SuppressLint("SetTextI18n")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        try {
            requestWindowFeature(Window.FEATURE_NO_TITLE)
            window?.setBackgroundDrawable(ColorDrawable(Color.TRANSPARENT))
            setContentView(R.layout.request_extension_dialog)
            window?.setLayout(WindowManager.LayoutParams.MATCH_PARENT,
                    WindowManager.LayoutParams.WRAP_CONTENT)

            // Initialize views
            okBtn = findViewById(R.id.okBtn)
            requestTitle = findViewById(R.id.requestTitle)
            requestDescription = findViewById(R.id.requestDescription)
            requestMessage = findViewById(R.id.requestMessage)
            payment_icon = findViewById(R.id.payment_icon)
            confirm_buttonLayout = findViewById(R.id.confirm_buttonLayout)
            yesBtn = findViewById(R.id.yesBtn)
            cancel = findViewById(R.id.cancel)

            okBtn.setOnClickListener(this)
            if (inWhich == REQUEST_DIALOG) {
                AppPreference.setRequestPeriod(c = context, key = "request_period")
                requestTitle.text = context.getString(R.string.your_request_has_been_sent)
                requestDescription.text = response
            } else {
                val user = AppPreference.getLoginUser(context)
                requestTitle.text = context.getString(R.string.please_confirm)
                okBtn.visibility = View.GONE
                payment_icon.setImageResource(R.mipmap.warning)
                confirm_buttonLayout.visibility = View.VISIBLE
                if (isReachSales!!) {
                    requestMessage.text = context.getString(R.string.sales_reach_request__message)
                } else {
                    if (user.plan == context.getString(R.string.trial)) {
                        if (user.enableRequestExtension && !AppPreference.isRequestPeriod(
                                        c = context, key = "request_period")!!) {
                            requestMessage.text = context.getString(R.string.request_trial_message)
                        } else {
                            requestMessage.text = context.getString(
                                    R.string.already_requested_trial_message) + " " + getRequestExtensionDate(
                                    user = user) + " " + context.getString(
                                    R.string.already_requested_extension_message)
                            requestDescription.visibility = View.GONE
                            requestTitle.text = context.getString(R.string.trial_extension)
                            okBtn.visibility = View.VISIBLE
                            confirm_buttonLayout.visibility = View.GONE
                        }
                    } else {

                        if (user.enableRequestExtension && !AppPreference.isRequestPeriod(
                                        c = context, key = "request_period")!!) {
                            requestMessage.text = context.getString(R.string.grace_request_message)
                        } else {
                            requestMessage.text = context.getString(
                                    R.string.already_requested_grace_message) + " " + getRequestExtensionDate(
                                    user = user) + " " + context.getString(
                                    R.string.already_requested_extension_message)
                            requestDescription.visibility = View.GONE
                            requestTitle.text = context.getString(R.string.grace_period)
                            okBtn.visibility = View.VISIBLE
                            confirm_buttonLayout.visibility = View.GONE
                        }
                    }
                }
                requestDescription.text = context.getString(R.string.are_you_sure_want)
                requestMessage.visibility = View.VISIBLE
                yesBtn.setOnClickListener(this)
                cancel.setOnClickListener(this)
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    private fun getRequestExtensionDate(user: User): String? {
        return if (AppPreference.isRequestPeriod(c = context,
                        key = "request_period")!!) {
            DateUtil.parseMMMformat(DateUtil.parseMMMDate(
                    AppPreference[context, "date", ""]))
        } else {
            val date = user.extensionRequestedDate!!.split(" ")
            DateUtil.parseMMMformat(
                    DateUtil.parseMMMDate(date[0]))
        }
    }

    override fun onClick(p0: View?) {
        if (p0?.id == R.id.cancel || p0?.id == R.id.okBtn) {
            dismiss()

        } else if (p0?.id == R.id.yesBtn) {
            callback?.onClick()
            dismiss()
        }
    }

    interface CallBack {
        fun onClick()
    }

    companion object {
        const val CONFIRM_DIALOG = 0
        const val REQUEST_DIALOG = 1
    }


}