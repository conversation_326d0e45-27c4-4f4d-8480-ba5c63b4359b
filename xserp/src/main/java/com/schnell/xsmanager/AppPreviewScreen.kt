package com.schnell.xsmanager

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.os.Parcelable
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.Button
import androidx.viewpager.widget.PagerAdapter
import androidx.viewpager.widget.ViewPager
import me.relex.circleindicator.CircleIndicator


/**
 * @since 21/5/21.
 * Launcher activity to decide which screen to launch then
 */

class AppPreviewScreen : Activity() {

    private var a: Activity? = null
    private lateinit var pager: ViewPager
    private lateinit var indicator: CircleIndicator
    private lateinit var next: Button
    private lateinit var skip: Button

    /**
     * Called when the activity is first preview screen created.
     */
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.app_preview)
        a = this
        val imagePagerAdapter = ImagePagerAdapter(activity = a!!)
        pager.adapter = imagePagerAdapter
        indicator.setViewPager(pager)
        var currentPosition = 0
        var isLastPageSwiped = false
        var counterPageScroll = 0
        pager.addOnPageChangeListener(object : ViewPager.OnPageChangeListener {
            override fun onPageScrollStateChanged(state: Int) {
            }

            override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) {
                if (position == imagePagerAdapter.count - 1 && positionOffset.toInt() == 0 && !isLastPageSwiped) {
                    if (counterPageScroll != 0) {
                        isLastPageSwiped = true
                        navigateLogin()
                    }
                    counterPageScroll++
                } else {
                    counterPageScroll = 0
                }
            }

            override fun onPageSelected(position: Int) {

                currentPosition = position

            }

        })
        next.setOnClickListener {
            if (pager.currentItem == imagePagerAdapter.count-1) {
                navigateLogin()
            } else {
                pager.currentItem = currentPosition+1

            }

        }
        skip.setOnClickListener {
            navigateLogin()
        }
    }

    private fun navigateLogin() {
        AppPreference.skipFlag(c = this, key = "skip")
        startActivity(Intent(a!!, LoginScreen::class.java))
        finish()
    }

    class ImagePagerAdapter(private val activity: Activity) : PagerAdapter() {
        private var images = arrayOf(R.mipmap.preview_sales,
                R.mipmap.preview_purchase_order, R.mipmap.preview_accounts, R.mipmap.preview_store,
                R.mipmap.preview_purchase)

        override fun instantiateItem(collection: ViewGroup, position: Int): Any {
            val inflater = LayoutInflater.from(activity)
            val layout = inflater.inflate(R.layout.app_preview_adapter,
                    collection, false) as ViewGroup
            val imageView: ImageView = layout
                    .findViewById(R.id.image) as ImageView

            imageView.setBackgroundResource(images[position])

            collection.addView(layout, 0)
            return layout
        }

        override fun destroyItem(collection: ViewGroup, position: Int, view: Any) {
            collection.removeView(view as View)
        }

        override fun getCount(): Int {
            return images.size
        }

        override fun isViewFromObject(view: View, `object`: Any): Boolean {
            return view == `object`
        }

        override fun restoreState(state: Parcelable?, loader: ClassLoader?) {}

        override fun saveState(): Parcelable? {
            return null
        }
    }

}