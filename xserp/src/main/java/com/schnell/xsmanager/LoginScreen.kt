package com.schnell.xsmanager

import android.app.Dialog
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.text.InputType
import android.text.SpannableString
import android.text.TextUtils
import android.text.style.UnderlineSpan
import android.util.Patterns
import android.view.View
import android.view.Window
import android.widget.EditText
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.core.content.ContextCompat
import com.google.android.material.snackbar.Snackbar
import com.schnell.http.Response
import com.schnell.http.ResponseListener
import com.schnell.util.Utility
import com.schnell.widget.AppDialogs
import com.schnell.widget.SignUpDialog
import com.schnell.widget.TextView
import com.schnell.xsmanager.model.User
import com.schnell.xsmanager.webservice.RegistrationService
import com.schnell.xsmanager.webservice.RegistrationService.Companion.registeredEmailVerification
import com.schnell.xsmanager.webservice.RequestManager
import com.schnell.xsmanager.webservice.UserService
import kotlinx.android.synthetic.main.activity_login.*
import kotlinx.android.synthetic.main.activity_login_popup.*



/**
 * @since 24/2/17.
 * Login screen to handle the authentication
 */

class LoginScreen : AppCompatActivity(), View.OnClickListener, ResponseListener {
    private var mDialog: ForgotPasswordDialog? = null
    private var user: User? = null
    private var LOGIN = 0
    private var FORGET_PASSWORD = 1
    private var data: Uri? = null
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_login)
        data = intent?.data
        initView()
        Utility.checkStoragePermission(this)
    }

    private fun initView() {
        findViewById<View>(R.id.signin).setOnClickListener(this)
        val forgetPassword = findViewById<TextView>(R.id.forgotPassword)
        forgetPassword.setOnClickListener(this)
        AppDialogs.hideSoftKeyboard(this)
        showBtn1.setColorFilter(ContextCompat.getColor(this, R.color.grey))
        showBtn2.setColorFilter(ContextCompat.getColor(this, R.color.grey))
        showBtn3.setColorFilter(ContextCompat.getColor(this, R.color.grey))
        versionInfo.text = "Version ${BuildConfig.VERSION_NAME}"
        if (data == null) {
            initViewStatus(intent.getIntExtra("password", 0))
        } else if (data.toString().contains("http")) {
            initViewStatus(intent.getIntExtra("password", 0))
            if (Utility.isInternetAvailable(this)) {
                val token = data.toString().split("=")
                registeredEmailVerification(this, token = token[1])
            } else {
                Snackbar.make(forgetPassword, getString(R.string.may_not_work),
                        Snackbar.LENGTH_LONG).show()
            }
        } else {
            initViewStatus(FORGET_PASSWORD)
        }
        val terms = findViewById<TextView>(R.id.terms)
        terms.setOnClickListener(this)
        val privacy = findViewById<TextView>(R.id.privacy)
        privacy.setOnClickListener(this)
        signUp.setOnClickListener(this)
        setUnderLine(forgetPassword)
        setUnderLine(terms)
        setUnderLine(privacy)
    }

    fun setUnderLine(view: TextView) {
        val content = SpannableString(view.text)
        content.setSpan(UnderlineSpan(), 0, content.length, 0)
        view.text = content
    }

    override fun onClick(view: View) {
        when (view.id) {
            R.id.signin -> {
                if (signin.text.toString() == resources.getString(R.string.change_password)) {
                    changePasswordValidate()
                } else {
                    AppDialogs.hideSoftKeyboard(this)
                    validate()
                }

            }
            R.id.forgotPassword -> {
                if (mDialog == null) {
                    mDialog = ForgotPasswordDialog(this)
                }
                mDialog!!.resetText()
                mDialog!!.show()
                val displayMetrics = resources.displayMetrics
                mDialog!!.window?.setLayout((displayMetrics.widthPixels - 20),
                        LinearLayout.LayoutParams.WRAP_CONTENT)
            }
            R.id.showBtn1 -> {
                initImageState(showBtn1, userEmail)
            }
            R.id.showBtn2 -> {
                initImageState(showBtn2, password)
            }
            R.id.showBtn3 -> {
                initImageState(showBtn3, confirmPassword)
            }
            R.id.terms -> {
                val i = Intent(Intent.ACTION_VIEW)
                i.data = Uri.parse(RequestManager.appendDomainAddress(UserService.API.terms))
                startActivity(i)
            }
            R.id.privacy -> {
                val i = Intent(Intent.ACTION_VIEW)
                i.data = Uri.parse(RequestManager.appendDomainAddress(UserService.API.privacy))
                startActivity(i)
            }
            R.id.signUp -> {
                startActivityForResult(Intent(this, SignUpScreen::class.java), 103)

            }
        }
    }

    private fun initImageState(image: ImageView, editText: EditText) {
        if (image.tag == "eyeOff") {
            image.setImageResource(R.mipmap.eye)
            image.tag = "eye"
            editText.inputType = InputType.TYPE_TEXT_VARIATION_VISIBLE_PASSWORD
        } else {
            image.setImageResource(R.mipmap.eye_off)
            image.tag = "eyeOff"
            editText.inputType = InputType.TYPE_CLASS_TEXT or InputType.TYPE_TEXT_VARIATION_PASSWORD
        }
    }

    private fun changePasswordValidate() {
        try {
            if (userEmail!!.visibility == View.VISIBLE && userEmail!!.text.toString().isEmpty()) {
                userEmail!!.error = resources.getString(R.string._password)
                userEmail!!.requestFocus()
            } else if (userEmail!!.visibility == View.VISIBLE && userEmail.text!!.length <= 5) {
                userEmail!!.error = resources.getString(R.string.min_password_length)
                userEmail!!.requestFocus()
            } else if (password.text!!.length <= 5) {
                password.error = resources.getString(R.string.min_password_length)
                password.requestFocus()
            } else if (confirmPassword!!.text!!.length <= 5) {
                confirmPassword!!.error = resources.getString(R.string.min_password_length)
                confirmPassword!!.requestFocus()
            } else if (password.text.toString().isEmpty()) {
                password.error = resources.getString(R.string._password)
                password.requestFocus()
            } else if (confirmPassword.text.toString().isEmpty()) {
                confirmPassword!!.error = resources.getString(R.string._password)
                confirmPassword!!.requestFocus()
            } else if (confirmPassword.text.toString() != password.text.toString()) {
                confirmPassword!!.error = resources.getString(R.string._password_mismatch)
                confirmPassword!!.requestFocus()

            } else {
                if (Utility.isInternetAvailable(this)) {
                    AppDialogs.showProgressDialog(this)
                    if (user == null) {
                        user = User()
                        val loginUser = AppPreference.getLoginUser(this)
                        user!!.email = loginUser.email
                    }
                    user!!.password = userEmail!!.text.toString()
                    user!!.confirmPassword = confirmPassword!!.text.toString()
                    var token = ""
                    if (data != null) {
                        token = data!!.getQueryParameter("u")!!
                    }
                    UserService.changePassword(this, user!!, cp_token = token)
                } else {
                    initEnableNetwork()
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /**
     * Validate and proceed login
     */
    private fun validate() {

        if (userEmail!!.text.toString().isEmpty()) {
            userEmail!!.error = resources.getString(R.string._email_id)
            userEmail!!.requestFocus()
        } else if (password.text.toString().isEmpty()) {
            password.error = resources.getString(R.string._password)
            password.requestFocus()
        } else {
            if (Utility.isInternetAvailable(this)) {
                AppDialogs.showProgressDialog(this)
                val u = User()
                u.email = userEmail!!.text.toString()
                u.password = password.text.toString()
                UserService.login(this, u)
            } else {
                initEnableNetwork()

            }
        }
    }

    private fun initEnableNetwork() {
        val l = View.OnClickListener {
            startActivityForResult(Intent(android.provider.Settings.ACTION_SETTINGS), 0)
        }

        val snack = Snackbar.make(userEmail!!, "No network found. Enable now!",
                Snackbar.LENGTH_LONG)
        snack.setAction("SETTINGS", l)
        snack.show()
    }

    override fun onResponse(r: Response?) {
        try {
            AppDialogs.hideProgressDialog()

            if (r == null) {
                Toast.makeText(this, "Could not connect to server", Toast.LENGTH_LONG).show()
            } else if (r.customMessage == getString(R.string.server_failed_msg)) {
                Utility.serverError(r = r, v = signin, context = this)
            } else if (r.requestType == UserService.API.login.hashCode()) {
                if (r.isSuccess) {
                    XSDashboard.REQUEST_CODE = 1
                    val u = r as User?
                    AppPreference.storeLoginUser(this, u)
                    AppPreference.put(this, getString(R.string.module_purchase), u!!.poCount!!)
                    AppPreference.put(this, "invoice_pending_count", u.invoicePendingCount!!)
                    AppPreference.put(this, "oa_pending_count", u.oaPendingCount!!)
                    AppPreference.put(this, getString(R.string.module_stores), u.receiptCount!!)
                    AppPreference.put(this, getString(R.string.module_auditing), u.icdCheckCount!!)
                    AppPreference.put(this, getString(R.string.module_masters),
                            u.masterPriceCount!!)
                    val i = Intent(baseContext, XSDashboard::class.java)
                    startActivity(i)
                    finish()
                } else {
                    Toast.makeText(this, r.customMessage, Toast.LENGTH_LONG).show()
                }
            } else if (r.requestType == RegistrationService.API.registrationEmailVerification.hashCode()) {
                if (r.isSuccess) {
                    showDialog(r, SignUpDialog.REGISTRATION_COMPLETION_DIALOG)
                } else {
                    showDialog(r, inWhich = SignUpDialog.EMAIL_FAILD_DIALOG)
                }

            } else if (r.requestType == UserService.API.forgotPassword.hashCode()) {
                if (r.isSuccess) {
                    // initViewStatus(CHANGEPASSWORD)
                    Toast.makeText(this, r.message + " " + r.customMessage,
                            Toast.LENGTH_LONG).show()
                } else {
                    Toast.makeText(this, r.message + " " + r.customMessage,
                            Toast.LENGTH_LONG).show()
                }
            } else if (r.requestType == UserService.API.changePassword.hashCode()) {
                if (r.isSuccess) {
                    XSDashboard.REQUEST_CODE = 2
                    initViewStatus(LOGIN)
                    Toast.makeText(this, r.message + " " + r.customMessage,
                            Toast.LENGTH_LONG).show()
                } else {
                    Toast.makeText(this, r.message + " " + r.customMessage,
                            Toast.LENGTH_LONG).show()
                }
            }
        } catch (e: java.lang.Exception) {
            e.printStackTrace()
        }
    }

    private fun initViewStatus(status: Int?) {
        userEmail!!.setText("")
        password.setText("")
        confirmPassword!!.setText("")
        if (status == LOGIN) {
            val user = AppPreference.getLoginUser(this)
            if (data == null) {
                if (user.id > -1) {
                    UserService.logout(this, user)
                }
                AppPreference.removeAll(this)
            }
            userEmail!!.inputType = InputType.TYPE_CLASS_TEXT
            userEmail!!.hint = resources.getString(R.string.user_email)
            password!!.hint = "Password"
            signin.text = resources.getString(R.string.sign_in)
            confirmPasswordLayout.visibility = View.GONE
            showBtn1.visibility = View.GONE
            showBtn2.visibility = View.GONE

        } else if (status == FORGET_PASSWORD) {
            if (data != null) {
                password!!.hint = resources.getString(R.string.new_password)
                confirmPassword!!.hint = resources.getString(R.string.confirm_new_password)
                signin.text = resources.getString(R.string.change_password)
                confirmPasswordLayout.visibility = View.VISIBLE
                userEmail!!.visibility = View.GONE
                forgotPassword.visibility = View.GONE
                showBtn2.visibility = View.VISIBLE
                user = AppPreference.getLoginUser(this)

            }
        } else {
            changePassword()
        }

    }

    private fun changePassword() {
        try {
            userEmail!!.visibility = View.VISIBLE
            showBtn1.visibility = View.VISIBLE
            userEmail!!.hint = resources.getString(R.string.old_password)
            userEmail!!.inputType = InputType.TYPE_CLASS_TEXT or InputType.TYPE_TEXT_VARIATION_PASSWORD
            password!!.hint = resources.getString(R.string.new_password)
            signin.text = resources.getString(R.string.change_password)
            forgotPassword.visibility = View.GONE
            signUp.visibility = View.GONE
            confirmPasswordLayout.visibility = View.VISIBLE
            showBtn2.visibility = View.VISIBLE
        } catch (e: Exception) {
            e.printStackTrace()
        }

    }

    fun forgotPassword(email: String) {
        if (Utility.isInternetAvailable(this)) {
            AppDialogs.showProgressDialog(this)
            val u = User()
            u.email = email
            this.user = u
            UserService.forgotPassword(this, u)
            AppPreference.storeLoginUser(this, u)
        } else {
            initEnableNetwork()
        }
    }

    inner class ForgotPasswordDialog(context: Context?) : Dialog(context!!), View.OnClickListener {
        private var a: Context? = null
        private var emailAddress: EditText? = null
        override fun onCreate(savedInstanceState: Bundle?) {
            super.onCreate(savedInstanceState)
            requestWindowFeature(Window.FEATURE_NO_TITLE)
            setContentView(R.layout.activity_login_popup)
            emailAddress = findViewById(R.id.emailAddress)
            sendEmail.setOnClickListener(this)
            this.a = context
        }

        fun resetText() {
            if (emailAddress != null)
                emailAddress!!.setText("")
        }

        override fun onClick(v: View?) {

            if (emailAddress!!.text.isEmpty()) {
                emailAddress!!.error = "Email Address is required"
            } else {
                if (isValidEmail(emailAddress!!.text)) {
                    forgotPassword(emailAddress!!.text.toString())
                    dismiss()
                } else {
                    emailAddress!!.error = "Please enter the valid email"
                }
            }

        }

        private fun isValidEmail(target: CharSequence): Boolean {
            return !TextUtils.isEmpty(target) && Patterns.EMAIL_ADDRESS.matcher(target).matches()
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        if (requestCode == 103 && data != null) {
            val response = data.getSerializableExtra("response") as Response
            showDialog(response, SignUpDialog.REGISTRATION_SUCCESS_DIALOG)

        }
        super.onActivityResult(requestCode, resultCode, data)
    }

    private fun showDialog(r: Response?, inWhich: Int = 0) {
        SignUpDialog(this, response = r, inWhich = inWhich,
                callback = object : SignUpDialog.CallBack {
                    override fun onClick() {
                    }
                }).show()

    }
}

